@extends('layouts.admin')

@section('title', 'แก้ไขแพคเกจ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.packages') }}">จัดการแพคเกจ</a></li>
<li class="breadcrumb-item active">แก้ไขแพคเกจ</li>
@endsection

@section('content')
<div class="content-safe-area">
<div class="prevent-excessive-scroll">
<div class="d-flex justify-content-between align-items-start mb-3">
    <div>
        <h1 class="h3 mb-1 text-primary">
            📝 แก้ไขแพคเกจ
        </h1>
        <p class="text-muted mb-0">แก้ไขข้อมูลแพคเกจ: {{ $package->name }}</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('packages') }}" class="btn btn-primary" target="_blank">
            ดูหน้าเว็บ
        </a>
        <a href="{{ route('admin.packages') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">
            หลับสู่หน้าแรก
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.packages.update', $package->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="page" value="{{ $page ?? 1 }}">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อแพคเกจ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('name') is-invalid @enderror" 
                               id="name" name="name" value="{{ old('name', $package->name) }}" required>
                        @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description', $package->description) }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">รายการที่รวมในแพคเกจ <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('features') is-invalid @enderror"
                                  id="features" name="features" rows="6" required>{{ old('features', $package->features) }}</textarea>
                        @error('features')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">แต่ละรายการขึ้นบรรทัดใหม่</div>
                    </div>

                    <div class="mb-3">
                        <label for="price_text" class="form-label">ราคา</label>
                        <input type="text" class="form-control @error('price_text') is-invalid @enderror"
                               id="price_text" name="price_text" value="{{ old('price_text', $package->price_text) }}"
                               placeholder="เช่น 50,000 บาท, ตามตกลง, เริ่มต้น 30,000 บาท, สอบถาม">
                        @error('price_text')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">สามารถใส่ตัวเลข ข้อความ หรือทั้งคู่ได้ หากไม่ใส่จะแสดงข้อความ "สอบถามราคา"</div>
                    </div>

                    <div class="mb-3">
                        <label for="duration" class="form-label">ระยะเวลา</label>
                        <input type="text" class="form-control @error('duration') is-invalid @enderror"
                               id="duration" name="duration" value="{{ old('duration', $package->duration) }}"
                               placeholder="เช่น ครั้งเดียว, 1 ปี, 6 เดือน">
                        @error('duration')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror" 
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $package->sort_order) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        @if($package->image)
                        <div class="mb-2">
                            <img src="{{ asset('storage/' . $package->image) }}" alt="{{ $package->name }}" 
                                 class="img-thumbnail" style="max-width: 200px;">
                            <div class="form-text">รูปภาพปัจจุบัน</div>
                        </div>
                        @endif
                        <input type="file" class="form-control @error('image') is-invalid @enderror" 
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยน)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           value="1" {{ old('is_featured', $package->is_featured) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_featured">
                                        แพคเกจแนะนำ
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {{ old('is_active', $package->is_active) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งาน
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="{{ route('admin.packages') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลแพคเกจ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>สร้างเมื่อ:</strong></td>
                        <td>{{ $package->created_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td>{{ $package->updated_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            @if($package->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                    </tr>
                    <tr>
                        <td><strong>แนะนำ:</strong></td>
                        <td>
                            @if($package->is_featured)
                            <span class="badge bg-warning text-dark">แนะนำ</span>
                            @else
                            <span class="text-muted">ไม่แนะนำ</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการ</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('packages') }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="{{ route('admin.packages.delete', $package->id) }}" method="POST"
                          id="deletePackageForm{{ $package->id }}">
                        @csrf
                        @method('DELETE')
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeletePackage({{ $package->id }})">
                            <i class="fas fa-trash me-2"></i>ลบแพคเกจ
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
// Delete package function with custom modal
async function handleDeletePackage(packageId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแพคเกจนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแพคเกจ'
    );

    if (confirmed) {
        document.getElementById(`deletePackageForm${packageId}`).submit();
    }
}
</script>
</div>
</div> <!-- ปิด content-safe-area -->
@endsection

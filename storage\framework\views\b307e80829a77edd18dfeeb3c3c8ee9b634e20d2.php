<?php $__env->startSection('title', 'จัดการผลงาน - Phuyai Prajak Service Shop Admin'); ?>

<?php $__env->startSection('content'); ?>
<div class="content-safe-area">
<!-- Header Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h2 mb-1" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)); -webkit-background-clip: text; -webkit-text-fill-color: transparent;">
                    <i class="fas fa-images me-2"></i>จัดการผลงาน
                </h1>
                <p class="text-muted mb-0">จัดการผลงานทั้งหมดของคุณ</p>
            </div>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" onclick="toggleView()">
                    <i class="fas fa-th-large me-2" id="viewIcon"></i>
                    <span id="viewText">มุมมองการ์ด</span>
                </button>
                <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4">
    <div class="col-md-4">
        <div class="card stats-card h-100">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">ผลงานทั้งหมด</h6>
                        <h3 class="mb-0"><?php echo e($totalActivities); ?></h3>
                    </div>
                    <i class="fas fa-images fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">ผลงานที่เปิดใช้</h6>
                        <h3 class="mb-0"><?php echo e($activeActivities); ?></h3>
                    </div>
                    <i class="fas fa-check-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
            <div class="card-body text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title text-white-50 mb-1">ผลงานที่ปิดใช้</h6>
                        <h3 class="mb-0"><?php echo e($totalActivities - $activeActivities); ?></h3>
                    </div>
                    <i class="fas fa-times-circle fa-2x text-white-50"></i>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- Activities Content -->
<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>รายการผลงาน
            </h5>
            <div class="d-flex gap-2">
                <div class="input-group" style="width: 300px;">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" placeholder="ค้นหาผลงาน..." id="searchInput">
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <?php if($activities->count() > 0): ?>

        <!-- Table View -->
        <div id="tableView">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">รูปภาพ</th>
                            <th>ชื่อผลงาน</th>
                            <th>คำอธิบาย</th>
                            <th style="width: 100px;">สถานะ</th>
                            <th style="width: 80px;">ลำดับ</th>
                            <th style="width: 150px;">การจัดการ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="activity-row">
                            <td>
                                <?php
                                    $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                                    $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                                ?>
                                <?php if($coverImagePath): ?>
                                <img src="<?php echo e(asset('storage/' . $coverImagePath)); ?>" alt="<?php echo e($activity->title); ?>"
                                     class="img-thumbnail rounded" style="width: 60px; height: 60px; object-fit: cover;">
                                <?php else: ?>
                                <div class="bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center rounded"
                                     style="width: 60px; height: 60px;">
                                    <i class="fas fa-image text-muted"></i>
                                </div>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div>
                                    <strong class="activity-title"><?php echo e($activity->title); ?></strong>
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <?php echo e($activity->created_at->format('d/m/Y')); ?>

                                    </small>
                                </div>
                            </td>
                            <td>
                                <span class="activity-description"><?php echo e(Str::limit($activity->description, 80)); ?></span>
                            </td>
                            <td>
                                <?php if($activity->is_active): ?>
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>เปิดใช้
                                </span>
                                <?php else: ?>
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>ปิดใช้
                                </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo e($activity->sort_order ?? 0); ?></span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="<?php echo e(route('admin.activities.edit', $activity->id)); ?><?php echo e(request()->has('page') ? '?page=' . request('page') : ''); ?>"
                                       class="btn btn-sm btn-outline-primary" title="แก้ไข">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="<?php echo e(route('admin.activities.delete', $activity->id)); ?>"
                                          method="POST" class="d-inline" id="deleteActivityTableForm<?php echo e($activity->id); ?>">
                                        <?php echo csrf_field(); ?>
                                        <?php echo method_field('DELETE'); ?>
                                        <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="handleDeleteActivityTable(<?php echo e($activity->id); ?>)" title="ลบ">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Card View -->
        <div id="cardView" style="display: none;">
            <div class="row g-4">
                <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-6 col-lg-4 activity-card">
                    <div class="card h-100 interactive-card">
                        <?php
                            $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                        ?>
                        <?php if($coverImagePath): ?>
                        <img src="<?php echo e(asset('storage/' . $coverImagePath)); ?>" class="card-img-top"
                             style="height: 200px; object-fit: cover;" alt="<?php echo e($activity->title); ?>">
                        <?php else: ?>
                        <div class="card-img-top bg-secondary bg-opacity-10 d-flex align-items-center justify-content-center"
                             style="height: 200px;">
                            <i class="fas fa-image fa-3x text-muted"></i>
                        </div>
                        <?php endif; ?>

                        <div class="card-body">
                            <h5 class="card-title activity-title"><?php echo e($activity->title); ?></h5>
                            <p class="card-text activity-description"><?php echo e(Str::limit($activity->description, 100)); ?></p>

                            <div class="d-flex justify-content-end align-items-center mb-3">
                                <?php if($activity->is_active): ?>
                                <span class="badge bg-success">เปิดใช้</span>
                                <?php else: ?>
                                <span class="badge bg-secondary">ปิดใช้</span>
                                <?php endif; ?>
                            </div>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('admin.activities.edit', $activity->id)); ?><?php echo e(request()->has('page') ? '?page=' . request('page') : ''); ?>"
                                   class="btn btn-primary flex-fill">
                                    <i class="fas fa-edit me-2"></i>แก้ไข
                                </a>
                                <form action="<?php echo e(route('admin.activities.delete', $activity->id)); ?>"
                                      method="POST" class="flex-fill" id="deleteActivityForm<?php echo e($activity->id); ?>">
                                    <?php echo csrf_field(); ?>
                                    <?php echo method_field('DELETE'); ?>
                                    <button type="button" class="btn btn-danger w-100"
                                            onclick="handleDeleteActivity(<?php echo e($activity->id); ?>)">
                                        <i class="fas fa-trash me-2"></i>ลบ
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
        </div>

        <!-- Pagination -->
        <?php if($activities->hasPages()): ?>
        <div class="mt-4 p-3 bg-light rounded">
            <?php echo $__env->make('custom.simple-pagination', ['paginator' => $activities], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h4 class="text-muted mb-2">ยังไม่มีผลงาน</h4>
            <p class="text-muted mb-4">เริ่มต้นด้วยการเพิ่มผลงานแรกของคุณ</p>
            <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
            </a>
        </div>
        <?php endif; ?>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
let currentView = 'table';

function toggleView() {
    const tableView = document.getElementById('tableView');
    const cardView = document.getElementById('cardView');
    const viewIcon = document.getElementById('viewIcon');
    const viewText = document.getElementById('viewText');

    if (currentView === 'table') {
        tableView.style.display = 'none';
        cardView.style.display = 'block';
        viewIcon.className = 'fas fa-list me-2';
        viewText.textContent = 'มุมมองตาราง';
        currentView = 'card';
    } else {
        tableView.style.display = 'block';
        cardView.style.display = 'none';
        viewIcon.className = 'fas fa-th-large me-2';
        viewText.textContent = 'มุมมองการ์ด';
        currentView = 'table';
    }
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const activityRows = document.querySelectorAll('.activity-row');
    const activityCards = document.querySelectorAll('.activity-card');

    // Search in table view
    activityRows.forEach(row => {
        const title = row.querySelector('.activity-title').textContent.toLowerCase();
        const description = row.querySelector('.activity-description').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });

    // Search in card view
    activityCards.forEach(card => {
        const title = card.querySelector('.activity-title').textContent.toLowerCase();
        const description = card.querySelector('.activity-description').textContent.toLowerCase();

        if (title.includes(searchTerm) || description.includes(searchTerm)) {
            card.style.display = '';
        } else {
            card.style.display = 'none';
        }
    });
});

// Add animation to cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.interactive-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});

// Delete activity function with custom modal (for card view)
async function handleDeleteActivity(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityForm${activityId}`).submit();
    }
}

// Delete activity function with custom modal (for table view)
async function handleDeleteActivityTable(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityTableForm${activityId}`).submit();
    }
}
</script>
</div> <!-- ปิด content-safe-area -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/activities/index.blade.php ENDPATH**/ ?>
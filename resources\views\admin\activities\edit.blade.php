@extends('layouts.admin')

@section('title', 'แก้ไขผลงาน - ผลงานการให้บริการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.activities') }}">ผลงานการให้บริการ</a></li>
<li class="breadcrumb-item active">แก้ไขผลงาน</li>
@endsection

@section('styles')
<style>
/* แก้ไขปัญหา layout สำหรับหน้าแก้ไขผลงาน */
.prevent-excessive-scroll {
    overflow: visible !important;
    height: auto !important;
}

/* ป้องกันการเสีย layout ของ Bootstrap columns */
.row {
    margin-left: 0 !important;
    margin-right: 0 !important;
}

.col-lg-8, .col-lg-4 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* ให้แน่ใจว่า col-lg-4 อยู่ด้านขวาใน desktop */
@media (min-width: 992px) {
    .col-lg-4 {
        order: 2; /* ให้อยู่ด้านขวา */
    }

    .col-lg-8 {
        order: 1; /* ให้อยู่ด้านซ้าย */
    }
}

/* สำหรับมือถือให้ col-lg-4 อยู่ด้านล่าง */
@media (max-width: 991.98px) {
    .col-lg-4 {
        order: 2;
        margin-top: 1rem;
    }

    .col-lg-8 {
        order: 1;
    }
}

/* ปรับปรุงการแสดงผลของ card */
.card {
    margin-bottom: 1.5rem;
    height: auto;
    overflow: visible;
}

.card-body {
    height: auto;
    overflow: visible;
}
</style>
@endsection

@section('content')
<div class="content-safe-area">
<div class="prevent-excessive-scroll">
<!-- Header Section - เรียบง่าย -->
<div class="d-flex justify-content-between align-items-start mb-3">
    <div>
        <h1 class="h3 mb-1 text-primary">แก้ไขผลงาน</h1>
        <p class="text-muted mb-0">แก้ไขข้อมูลผลงานของคุณ</p>
    </div>
    <div class="d-flex gap-2">
        <a href="{{ route('activities.show', $activity->id) }}" class="btn btn-primary" target="_blank">
            ดูหน้าเว็บ
        </a>
        <a href="{{ route('admin.activities') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">
            หลับสู่หน้าแรก
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.activities.update', $activity->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="page" value="{{ $page ?? 1 }}">
                    


                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                               id="title" name="title" value="{{ old('title', $activity->title) }}" required
                               placeholder="เช่น ภาพบรรยากาศงานเปิดตัวผลิตภัณฑ์">
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3" required
                                  placeholder="เขียนคำอธิบายสั้นๆ เกี่ยวกับผลงานนี้...">{{ old('description', $activity->description) }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror"
                                  id="details" name="details" rows="5"
                                  placeholder="เขียนรายละเอียดเพิ่มเติมเกี่ยวกับผลงานนี้...">{{ old('details', $activity->details) }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activity_date" class="form-label">วันที่ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('activity_date') is-invalid @enderror"
                                       id="activity_date" name="activity_date" value="{{ old('activity_date', $activity->activity_date->format('Y-m-d')) }}" required>
                                @error('activity_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror"
                                       id="location" name="location" value="{{ old('location', $activity->location) }}"
                                       placeholder="เช่น วัดพระแก้ว กรุงเทพฯ">
                                @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $activity->sort_order) }}" min="0"
                               placeholder="0 = แสดงก่อน, ตัวเลขมาก = แสดงหลัง">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">ใช้สำหรับจัดเรียงลำดับการแสดงในแกลลอรี่</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" {{ old('is_active', $activity->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-eye me-1"></i>เผยแพร่รูปภาพนี้ในแกลลอรี่
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกแต่ไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <!-- Current Gallery -->
                    @if($activity->images->count() > 0)
                    <div class="mb-4">
                        <label class="form-label">แกลเลอรี่รูปภาพปัจจุบัน</label>
                        <div class="row g-2" id="currentGallery">
                            @foreach($activity->images->sortBy('sort_order') as $image)
                            <div class="col-md-3 col-4" id="image-{{ $image->id }}">
                                <div class="card position-relative">
                                    <img src="{{ asset('storage/' . $image->image_path) }}"
                                         class="card-img-top"
                                         alt="{{ $image->alt_text }}"
                                         style="height: 120px; object-fit: cover; cursor: pointer;"
                                         onclick="viewImage('{{ asset('storage/' . $image->image_path) }}', '{{ $image->alt_text }}')">

                                    @if($image->is_cover)
                                    <div class="position-absolute top-0 end-0 m-1">
                                        <span class="badge bg-primary">รูปปก</span>
                                    </div>
                                    @endif

                                    <div class="card-body p-2">
                                        <div class="d-flex gap-1">
                                            @if(!$image->is_cover)
                                            <button type="button" class="btn btn-sm btn-outline-primary flex-fill"
                                                    onclick="setCoverImage({{ $image->id }})"
                                                    title="ตั้งเป็นรูปปก">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            @endif
                                            <button type="button" class="btn btn-sm btn-outline-danger flex-fill"
                                                    onclick="deleteImage({{ $image->id }})"
                                                    title="ลบรูป">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @else
                    <div class="mb-4">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            ยังไม่มีรูปภาพในแกลเลอรี่ กรุณาเพิ่มรูปภาพด้านล่าง
                        </div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพหลักใหม่ <span class="text-muted">(เปลี่ยนรูปปก)</span></label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยนรูปปก)</div>
                    </div>

                    <div class="mb-3">
                        <label for="new_gallery_images" class="form-label">เพิ่มรูปภาพแกลเลอรี่ใหม่</label>
                        <input type="file" class="form-control @error('new_gallery_images.*') is-invalid @enderror"
                               id="new_gallery_images" name="new_gallery_images[]" accept="image/*" multiple>
                        @error('new_gallery_images.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <i class="fas fa-info-circle text-primary me-1"></i>
                            สามารถเลือกหลายรูปพร้อมกัน | รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                        </div>

                        <!-- Preview Area -->
                        <div id="new-gallery-preview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปใหม่ที่เลือก:</label>
                            <div class="row g-2" id="new-preview-container"></div>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>คำแนะนำ:</strong>
                        <ul class="mb-0 mt-2">
                            <li><strong>เปลี่ยนรูปปก:</strong> ใช้สำหรับเปลี่ยนรูปหลักที่แสดงในหน้าแรกและรายการผลงาน</li>
                            <li><strong>เพิ่มรูปในแกลเลอรี่:</strong> ใช้สำหรับเพิ่มรูปเข้าไปในแกลเลอรี่ย่อยของผลงานนี้</li>
                            <li>สามารถทำทั้งสองอย่างพร้อมกันได้ในการบันทึกครั้งเดียว</li>
                        </ul>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="{{ route('admin.activities') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>ข้อมูลรูปภาพ
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>อัพโหลดเมื่อ:</strong></td>
                        <td>{{ $activity->created_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td>{{ $activity->updated_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>ลำดับการแสดง:</strong></td>
                        <td><span class="badge bg-info">{{ $activity->sort_order ?? 0 }}</span></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            @if($activity->is_active)
                            <span class="badge bg-success">
                                <i class="fas fa-eye me-1"></i>เผยแพร่
                            </span>
                            @else
                            <span class="badge bg-secondary">
                                <i class="fas fa-eye-slash me-1"></i>ซ่อน
                            </span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools text-warning me-2"></i>การดำเนินการ
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('activities') }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-external-link-alt me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="{{ route('admin.activities.delete', $activity->id) }}" method="POST"
                          id="deleteActivityForm{{ $activity->id }}">
                        @csrf
                        @method('DELETE')
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeleteActivity({{ $activity->id }})">
                            <i class="fas fa-trash me-2"></i>ลบผลงาน
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>



@section('scripts')
<script>
// Stable Image Gallery Management
function viewImage(src, title) {
    // Clean up any existing modal
    const modalElement = document.getElementById('imageModal');
    const existingModal = bootstrap.Modal.getInstance(modalElement);
    if (existingModal) {
        existingModal.dispose();
    }

    // Set image and title
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalDescription').textContent = '';

    // Create new modal instance
    const modal = new bootstrap.Modal(modalElement, {
        backdrop: true,
        keyboard: true,
        focus: true
    });

    modal.show();

    // Add cleanup event
    modalElement.addEventListener('hidden.bs.modal', function() {
        cleanupAdminModal();
    }, { once: true });
}

function cleanupAdminModal() {
    const modalElement = document.getElementById('imageModal');

    // Clean up body
    document.body.classList.remove('modal-open');
    document.body.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');

    // Remove backdrops
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());

    // Dispose modal instance
    const modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.dispose();
    }
}

async function deleteImage(imageId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบรูปภาพ'
    );

    if (confirmed) {
        fetch(`/admin/activities/images/${imageId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById(`image-${imageId}`).remove();
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('เกิดข้อผิดพลาดในการลบรูปภาพ', 'error');
        });
    }
}

function setCoverImage(imageId) {
    if (confirm('ตั้งรูปนี้เป็นรูปปกหรือไม่?')) {
        fetch(`/admin/activities/images/${imageId}/set-cover`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to update cover badges
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('เกิดข้อผิดพลาดในการตั้งรูปปก', 'error');
        });
    }
}

function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// New gallery images preview
document.addEventListener('DOMContentLoaded', function() {
    const newGalleryInput = document.getElementById('new_gallery_images');
    const newPreviewArea = document.getElementById('new-gallery-preview');
    const newPreviewContainer = document.getElementById('new-preview-container');

    if (newGalleryInput) {
        newGalleryInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
                newPreviewArea.style.display = 'block';
                newPreviewContainer.innerHTML = '';

                files.forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const col = document.createElement('div');
                            col.className = 'col-md-2 col-4';

                            col.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top"
                                         style="height: 100px; object-fit: cover;"
                                         alt="Preview ${index + 1}">
                                    <div class="card-body p-2">
                                        <small class="text-muted">รูปใหม่ที่ ${index + 1}</small>
                                    </div>
                                </div>
                            `;

                            newPreviewContainer.appendChild(col);
                        };

                        reader.readAsDataURL(file);
                    }
                });
            } else {
                newPreviewArea.style.display = 'none';
            }
        });
    }
});

// Delete activity function with custom modal
async function handleDeleteActivity(activityId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบผลงานนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบผลงาน'
    );

    if (confirmed) {
        document.getElementById(`deleteActivityForm${activityId}`).submit();
    }
}
</script>

<script>
function setCoverImage(imageId) {
    if (confirm('ตั้งรูปนี้เป็นรูปปกหรือไม่?')) {
        fetch(`/admin/activities/images/${imageId}/set-cover`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to update cover badges
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('เกิดข้อผิดพลาดในการตั้งรูปปก', 'error');
        });
    }
}

function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// New gallery images preview
document.addEventListener('DOMContentLoaded', function() {
    // Initialize stable modal handlers
    const modalElement = document.getElementById('imageModal');
    if (modalElement) {
        const closeBtn = modalElement.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.preventDefault();
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                } else {
                    cleanupAdminModal();
                }
            });
        }

        // Backdrop click
        modalElement.addEventListener('click', function(e) {
            if (e.target === modalElement) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                } else {
                    cleanupAdminModal();
                }
            }
        });

        // Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modalElement.classList.contains('show')) {
                const modal = bootstrap.Modal.getInstance(modalElement);
                if (modal) {
                    modal.hide();
                } else {
                    cleanupAdminModal();
                }
            }
        });
    }

    const newGalleryInput = document.getElementById('new_gallery_images');
    const newPreviewArea = document.getElementById('new-gallery-preview');
    const newPreviewContainer = document.getElementById('new-preview-container');

    if (newGalleryInput) {
        newGalleryInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
                newPreviewArea.style.display = 'block';
                newPreviewContainer.innerHTML = '';

                files.forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const col = document.createElement('div');
                            col.className = 'col-md-2 col-4';

                            col.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top"
                                         style="height: 100px; object-fit: cover;"
                                         alt="Preview ${index + 1}">
                                    <div class="card-body p-2">
                                        <small class="text-muted">รูปใหม่ที่ ${index + 1}</small>
                                    </div>
                                </div>
                            `;

                            newPreviewContainer.appendChild(col);
                        };

                        reader.readAsDataURL(file);
                    }
                });
            } else {
                newPreviewArea.style.display = 'none';
            }
        });
    }
});
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">รูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3" style="max-height: 500px;">
                <p id="modalDescription" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>
</div>
</div> <!-- ปิด content-safe-area -->

@endsection

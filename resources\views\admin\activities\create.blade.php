@extends('layouts.admin')

@section('title', 'เพิ่มผลงานใหม่ - ผลงานการให้บริการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.activities') }}">ผลงานการให้บริการ</a></li>
<li class="breadcrumb-item active">เพิ่มผลงานใหม่</li>
@endsection

@section('content')
<div class="content-safe-area">
<!-- Header Section - เรียบง่าย -->
<div class="row mb-4">
    <div class="col-12">
        <div class="bg-white rounded-3 p-4 border">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-1 text-primary">
                        <i class="fas fa-plus me-2"></i>เพิ่มผลงานใหม่
                    </h1>
                    <p class="text-muted mb-0">สร้างผลงานการให้บริการพร้อมแกลเลอรี่รูปภาพ</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('admin.activities') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>กลับไปผลงาน
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.activities.store') }}" method="POST" enctype="multipart/form-data">
                    @csrf

                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพหลัก (รูปปก)</label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (รูปนี้จะเป็นรูปปกของผลงาน)</div>
                    </div>

                    <div class="mb-3">
                        <label for="gallery_images" class="form-label">รูปภาพแกลเลอรี่ <span class="text-muted">(เพิ่มเติม)</span></label>
                        <input type="file" class="form-control @error('gallery_images.*') is-invalid @enderror"
                               id="gallery_images" name="gallery_images[]" accept="image/*" multiple>
                        @error('gallery_images.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <i class="fas fa-info-circle text-primary me-1"></i>
                            สามารถเลือกหลายรูปพร้อมกัน (Ctrl+Click หรือ Shift+Click) | รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                        </div>

                        <!-- Preview Area -->
                        <div id="gallery-preview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปที่เลือก:</label>
                            <div class="row g-2" id="preview-container"></div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อเรื่อง <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                               id="title" name="title" value="{{ old('title') }}" required
                               placeholder="เช่น ภาพบรรยากาศงานเปิดตัวผลิตภัณฑ์">
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror"
                                  id="description" name="description" rows="3" required
                                  placeholder="เขียนคำอธิบายสั้นๆ เกี่ยวกับผลงานนี้...">{{ old('description') }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror"
                                  id="details" name="details" rows="5"
                                  placeholder="เขียนรายละเอียดเพิ่มเติมเกี่ยวกับผลงานนี้...">{{ old('details') }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="activity_date" class="form-label">วันที่ <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('activity_date') is-invalid @enderror"
                                       id="activity_date" name="activity_date" value="{{ old('activity_date', now()->format('Y-m-d')) }}" required>
                                @error('activity_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location" class="form-label">สถานที่</label>
                                <input type="text" class="form-control @error('location') is-invalid @enderror"
                                       id="location" name="location" value="{{ old('location') }}"
                                       placeholder="เช่น วัดพระแก้ว กรุงเทพฯ">
                                @error('location')
                                <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', 0) }}" min="0"
                               placeholder="0 = แสดงก่อน, ตัวเลขมาก = แสดงหลัง">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">ใช้สำหรับจัดเรียงลำดับการแสดงในแกลลอรี่</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active"
                                   value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                <i class="fas fa-eye me-1"></i>เผยแพร่รูปภาพนี้ในแกลลอรี่
                            </label>
                        </div>
                        <div class="form-text">หากไม่เลือก รูปภาพจะถูกบันทึกแต่ไม่แสดงในหน้าเว็บไซต์</div>
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary" id="submitBtn" data-original-text="บันทึกผลงาน">
                            <i class="fas fa-save me-2"></i>บันทึกผลงาน
                        </button>
                        <a href="{{ route('admin.activities') }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle text-info me-2"></i>ข้อมูลการสร้าง
                </h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>วันที่สร้าง:</strong></td>
                        <td>{{ now()->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>ลำดับเริ่มต้น:</strong></td>
                        <td><span class="badge bg-info">0</span></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะเริ่มต้น:</strong></td>
                        <td>
                            <span class="badge bg-success">
                                <i class="fas fa-eye me-1"></i>เผยแพร่
                            </span>
                        </td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-lightbulb text-warning me-2"></i>คำแนะนำ
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>คำแนะนำการสร้างผลงาน:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>รูปภาพแรก:</strong> จะถูกตั้งเป็นรูปปกอัตโนมัติ</li>
                        <li><strong>ชื่อเรื่อง:</strong> ใช้ชื่อที่สื่อความหมายชัดเจน</li>
                        <li><strong>คำอธิบาย:</strong> เขียนให้น่าสนใจและครบถ้วน</li>
                        <li><strong>ขนาดไฟล์:</strong> ไม่เกิน 2MB ต่อรูป</li>
                        <li><strong>ไฟล์ที่รองรับ:</strong> JPG, PNG, GIF</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery images preview
    const galleryInput = document.getElementById('gallery_images');
    const previewArea = document.getElementById('gallery-preview');
    const previewContainer = document.getElementById('preview-container');

    if (galleryInput) {
        galleryInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
                previewArea.style.display = 'block';
                previewContainer.innerHTML = '';

                files.forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const col = document.createElement('div');
                            col.className = 'col-md-2 col-4';

                            col.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top"
                                         style="height: 100px; object-fit: cover;"
                                         alt="Preview ${index + 1}">
                                    <div class="card-body p-2">
                                        <small class="text-muted">รูปที่ ${index + 1}</small>
                                    </div>
                                </div>
                            `;

                            previewContainer.appendChild(col);
                        };

                        reader.readAsDataURL(file);
                    }
                });
            } else {
                previewArea.style.display = 'none';
            }
        });
    }

    // Main image preview
    const mainImageInput = document.getElementById('image');
    if (mainImageInput) {
        mainImageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    // Remove existing preview
                    const existingPreview = document.getElementById('main-image-preview');
                    if (existingPreview) {
                        existingPreview.remove();
                    }

                    // Create new preview
                    const preview = document.createElement('div');
                    preview.id = 'main-image-preview';
                    preview.className = 'mt-2';
                    preview.innerHTML = `
                        <img src="${e.target.result}" class="img-thumbnail"
                             style="max-width: 200px; max-height: 150px;"
                             alt="ตัวอย่างรูปหลัก">
                        <div class="form-text">ตัวอย่างรูปหลัก</div>
                    `;

                    mainImageInput.parentNode.appendChild(preview);
                };

                reader.readAsDataURL(file);
            }
        });
    }

    // Form validation
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();

            if (!title || !description) {
                e.preventDefault();
                alert('กรุณากรอกข้อมูลให้ครบถ้วน');
                return false;
            }
        });
    }
});
</script>
@endsection
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);

            cardBody.appendChild(fileName);
            cardBody.appendChild(fileSize);
            cardBody.appendChild(badge);
            cardDiv.appendChild(img);
            cardDiv.appendChild(cardBody);
            colDiv.appendChild(cardDiv);
            additionalImagesPreview.appendChild(colDiv);
        });
    });

    // Auto-generate title from first filename
    imagesInput.addEventListener('change', function(e) {
        const files = e.target.files;
        const titleInput = document.getElementById('title');

        if (files.length > 0 && !titleInput.value) {
            // Remove file extension and format filename
            let filename = files[0].name.replace(/\.[^/.]+$/, "");
            filename = filename.replace(/[-_]/g, ' ');
            filename = filename.charAt(0).toUpperCase() + filename.slice(1);
            titleInput.value = filename;
        }
    });

    // Enhanced Form Submission
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');

    form.addEventListener('submit', function(e) {
        // Show loading state
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';

        // Add smooth loading animation
        submitBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            submitBtn.style.transform = 'scale(1)';
        }, 150);

        // Show page loading overlay
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="text-center">
                <div class="loading-spinner"></div>
                <p class="mt-3 text-muted">กำลังบันทึกผลงาน...</p>
                <small class="text-muted">กรุณารอสักครู่</small>
            </div>
        `;
        document.body.appendChild(overlay);

        // Show overlay with animation
        setTimeout(() => {
            overlay.classList.add('show');
        }, 100);

        // Re-enable button after timeout (fallback)
        setTimeout(() => {
            if (submitBtn.disabled) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>' + submitBtn.dataset.originalText;
                overlay.remove();
            }
        }, 10000); // 10 seconds timeout
    });

    // Add smooth focus effects to form controls
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.15)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>
</div> <!-- ปิด content-safe-area -->
@endsection

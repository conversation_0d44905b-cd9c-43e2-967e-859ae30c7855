@extends('layouts.admin')

@section('title', 'แก้ไขแบนเนอร์')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">แก้ไขแบนเนอร์: {{ $banner->title }}</h3>
                    <div class="card-tools">
                        <div class="d-flex gap-2">
                            <a href="{{ route('home') }}" class="btn btn-outline-primary" target="_blank">
                                <i class="fas fa-eye me-2"></i>ดูหน้าเว็บ
                            </a>
                            <a href="{{ route('admin.banners.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>หลับสู่หน้าแรก
                            </a>
                        </div>
                    </div>
                </div>

                <form action="{{ route('admin.banners.update', $banner) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="title" class="form-label">ชื่อแบนเนอร์ <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control @error('title') is-invalid @enderror" 
                                           id="title" 
                                           name="title" 
                                           value="{{ old('title', $banner->title) }}" 
                                           required>
                                    @error('title')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="description" class="form-label">คำอธิบาย</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" 
                                              name="description" 
                                              rows="3">{{ old('description', $banner->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>



                                <div class="mb-3">
                                    <label class="form-label">แสดงในหน้า</label>
                                    <div class="form-text mb-2">เลือกหน้าที่ต้องการให้แสดงแบนเนอร์นี้ (ถ้าไม่เลือกจะแสดงในทุกหน้า)</div>
                                    @php
                                        $pages = [
                                            'home' => 'หน้าหลัก',
                                            'services' => 'บริการ',
                                            'packages' => 'แพ็คเกจ',
                                            'activities' => 'ผลงาน',
                                            'contact' => 'ติดต่อเรา'
                                        ];
                                        $oldPages = old('display_pages', $banner->display_pages ?? []);
                                    @endphp
                                    @foreach($pages as $key => $name)
                                    <div class="form-check">
                                        <input class="form-check-input"
                                               type="checkbox"
                                               id="page_{{ $key }}"
                                               name="display_pages[]"
                                               value="{{ $key }}"
                                               {{ in_array($key, $oldPages) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="page_{{ $key }}">
                                            {{ $name }}
                                        </label>
                                    </div>
                                    @endforeach
                                    @error('display_pages')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="image" class="form-label">รูปภาพแบนเนอร์</label>
                                    <input type="file" 
                                           class="form-control @error('image') is-invalid @enderror" 
                                           id="image" 
                                           name="image" 
                                           accept="image/*">
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">รองรับไฟล์: JPG, PNG, GIF (ขนาดไม่เกิน 2MB) - เว้นว่างไว้หากไม่ต้องการเปลี่ยน</div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                                    <input type="number" 
                                           class="form-control @error('sort_order') is-invalid @enderror" 
                                           id="sort_order" 
                                           name="sort_order" 
                                           value="{{ old('sort_order', $banner->sort_order) }}" 
                                           min="0">
                                    @error('sort_order')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">เลขน้อยจะแสดงก่อน</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" 
                                               type="checkbox" 
                                               id="is_active" 
                                               name="is_active" 
                                               value="1" 
                                               {{ old('is_active', $banner->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            เปิดใช้งาน
                                        </label>
                                    </div>
                                </div>

                                <!-- Current Image -->
                                <div class="mb-3">
                                    <label class="form-label">รูปภาพปัจจุบัน</label>
                                    <div class="border rounded p-3 text-center">
                                        @if($banner->image_path)
                                            <img src="{{ asset('storage/' . $banner->image_path) }}" 
                                                 alt="{{ $banner->title }}" 
                                                 class="img-fluid" 
                                                 style="max-height: 150px;">
                                        @else
                                            <i class="fas fa-image fa-3x text-muted"></i>
                                            <p class="text-muted mt-2">ไม่มีรูปภาพ</p>
                                        @endif
                                    </div>
                                </div>

                                <!-- Preview New Image -->
                                <div class="mb-3">
                                    <label class="form-label">ตัวอย่างรูปภาพใหม่</label>
                                    <div id="image-preview" class="border rounded p-3 text-center" style="min-height: 150px; display: none;">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> บันทึกการแก้ไข
                        </button>
                        <a href="{{ route('admin.banners.index') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> ยกเลิก
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// Preview รูปภาพ
document.getElementById('image').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('image-preview');

    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" class="img-fluid" style="max-height: 150px;">`;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        preview.style.display = 'none';
    }
});


</script>
@endsection
